<?php

namespace App\Http\Controllers;

use App\Models\AgnetReport;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class AgentLoginReportController extends Controller
{
    public function formatTimestamp($timestamp)
    {
        $carbonDate = Carbon::parse($timestamp);
        return $carbonDate->format('M j, Y, g:i A');
    }

    public function getAgentLoginReportOld(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $request->validate([
                'date' => ['required', 'date_format:Y-m-d']
            ]);

            $loginTime = 0;
            $logoutTime = 0;
            $loginTimeEvent = 0;
            $logoutTimeEvent = 0;
            $data = '';
            $temp = [];

            $agents = AgnetReport::whereDate("time", "=", $request->date)->distinct()->where('Agent', '!=', 'NONE')->when($request->queue, function ($query) use ($request) {
                $query->where('queuename', $request->queue);
            })
                ->pluck('Agent')
                ->toArray();

            $data = DB::table('queue_log')
                ->select('Event', 'time', 'Agent', 'queuename')
                ->whereIn('Agent', $agents)
                ->whereIn('Event', ['ADDMEMBER', 'REMOVEMEMBER'])
                ->whereDate('time', $request->date)
                ->orderBy('time', 'ASC')
                ->get();

            $agentData = [];
            foreach ($data as $entry) {
                $agentData[$entry->Agent][] = $entry;
            }

            foreach ($agentData as $agentName => $entries) {
                // $agentIds = explode('/', $agentName);
                // $agentCode = $agentIds[1];

                if (strpos($agentName, '/') !== false) {
                    $agentIds = explode('/', $agentName);
                    $agentCode = $agentIds[1];
                } else {
                    $agentCode = $agentName;
                }

                $agentData = User::where('auth_username', '=', $agentCode)->first();

                if ($agentData) {
                    $agentName = $agentData->name;
                }

                foreach ($entries as $i => $entry) {
                    if ($entry->Event === 'ADDMEMBER') {
                        $loginTimeEvent = $this->formatTimestamp($entry->time);
                        $loginTime = $entry->time;
                    } elseif ($entry->Event === 'REMOVEMEMBER' && $loginTime !== null) {
                        $logoutTimeEvent = $this->formatTimestamp($entry->time);
                        $logoutTime = $entry->time;

                        // Check if REMOVEMEMBER time is on the next day
                        if (date('Y-m-d', strtotime($logoutTime)) != date('Y-m-d', strtotime($loginTime))) {
                            $temp[] = [
                                'agent' => $agentName,
                                'login_time' => $loginTimeEvent,
                                'queuename' => $entry->queuename,
                                'logout_time' => 'Logout on the next day',
                                'time_diff' => ''
                            ];
                        } else {
                            $loginTime = Carbon::parse($logoutTime)->diffInSeconds($loginTime);
                            $loginSeconds = round($loginTime);
                            $formattedTimeDiff = sprintf('%02d:%02d:%02d', ($loginSeconds / 3600), ($loginSeconds / 60 % 60), $loginSeconds % 60);

                            $temp[] = [
                                'agent' => $agentName,
                                'login_time' => $loginTimeEvent,
                                'queuename' => $entry->queuename,
                                'logout_time' => $logoutTimeEvent,
                                'time_diff' => $formattedTimeDiff
                            ];
                        }

                        // Reset login time after processing a pair
                        $loginTime = null;
                    }
                }

                // Handle case where there's a login without a corresponding logout
                if ($loginTime !== null) {
                    $logoutTime = Carbon::parse($loginTime)->endOfDay();
                    $loginTime = Carbon::parse($logoutTime)->diffInSeconds($loginTime);
                    $loginSeconds = round($loginTime);
                    $loginOutput = sprintf('%02d:%02d:%02d', ($loginSeconds / 3600), ($loginSeconds / 60 % 60), $loginSeconds % 60);

                    $logoutTimeEvent = $this->formatTimestamp($logoutTime);

                    $temp[] = [
                        'agent' => $agentName,
                        'login_time' => $loginTimeEvent,
                        'queuename' => $entries[0]->queuename,
                        'logout_time' => $logoutTimeEvent,
                        'time_diff' => $loginOutput
                    ];
                }
            }

            return response()->json($temp);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }

    public function getAgentLoginReport(Request $request): \Illuminate\Http\JsonResponse
    {
        try {
            $request->validate([
                'from' => 'nullable|date_format:Y-m-d',
                'to' => 'nullable|date_format:Y-m-d',
                'queue' => 'nullable|string',
                'date' => 'nullable|date_format:Y-m-d',
            ]);

            if ((!$request->from || !$request->to) && !$request->date) {
                return response()->json(['error' => 'Either "from" and "to" OR "date" is required.'], 422);
            }

            if ($request->filled('from') && $request->filled('to')) {
                $startDate = Carbon::parse($request->from)->startOfDay();
                $endDate = Carbon::parse($request->to)->endOfDay();
            } else {
                $startDate = Carbon::parse($request->date)->startOfDay();
                $endDate = Carbon::parse($request->date)->endOfDay();
            }

            $loginTime = 0;
            $logoutTime = 0;
            $loginTimeEvent = 0;
            $logoutTimeEvent = 0;
            $temp = [];

            $agents = AgnetReport::whereBetween('time', [$startDate, $endDate])
                ->distinct()
                ->where('Agent', '!=', 'NONE')
                ->when($request->queue, function ($query) use ($request) {
                    $query->where('queuename', $request->queue);
                })
                ->pluck('Agent')
                ->toArray();

            $data = DB::table('queue_log')
                ->select('Event', 'time', 'Agent', 'queuename')
                ->whereIn('Agent', $agents)
                ->whereIn('Event', ['ADDMEMBER', 'REMOVEMEMBER'])
                ->whereBetween('time', [$startDate, $endDate])
                ->orderBy('time', 'ASC')
                ->get();

            $agentData = [];
            foreach ($data as $entry) {
                $agentData[$entry->Agent][] = $entry;
            }

            foreach ($agentData as $agentName => $entries) {
                if (strpos($agentName, '/') !== false) {
                    $agentIds = explode('/', $agentName);
                    $agentCode = $agentIds[1];
                } else {
                    $agentCode = $agentName;
                }

                $agent = User::where('auth_username', '=', $agentCode)->first();
                if ($agent) {
                    $agentName = $agent->name;
                }

                foreach ($entries as $i => $entry) {
                    if ($entry->Event === 'ADDMEMBER') {
                        $loginTimeEvent = $this->formatTimestamp($entry->time);
                        $loginTime = $entry->time;
                    } elseif ($entry->Event === 'REMOVEMEMBER' && $loginTime !== null) {
                        $logoutTimeEvent = $this->formatTimestamp($entry->time);
                        $logoutTime = $entry->time;

                        if (date('Y-m-d', strtotime($logoutTime)) != date('Y-m-d', strtotime($loginTime))) {
                            $temp[] = [
                                'agent' => $agentName,
                                'login_time' => $loginTimeEvent,
                                'queuename' => $entry->queuename,
                                'logout_time' => 'Logout on the next day',
                                'time_diff' => ''
                            ];
                        } else {
                            $seconds = Carbon::parse($logoutTime)->diffInSeconds($loginTime);
                            $formattedTimeDiff = sprintf(
                                '%02d:%02d:%02d',
                                ($seconds / 3600),
                                ($seconds / 60 % 60),
                                $seconds % 60
                            );

                            $temp[] = [
                                'agent' => $agentName,
                                'login_time' => $loginTimeEvent,
                                'queuename' => $entry->queuename,
                                'logout_time' => $logoutTimeEvent,
                                'time_diff' => $formattedTimeDiff
                            ];
                        }

                        $loginTime = null;
                    }
                }

                if ($loginTime !== null) {
                    $logoutTime = Carbon::parse($loginTime)->endOfDay();
                    $seconds = Carbon::parse($logoutTime)->diffInSeconds($loginTime);
                    $formatted = sprintf(
                        '%02d:%02d:%02d',
                        ($seconds / 3600),
                        ($seconds / 60 % 60),
                        $seconds % 60
                    );

                    $logoutTimeEvent = $this->formatTimestamp($logoutTime);

                    $temp[] = [
                        'agent' => $agentName,
                        'login_time' => $loginTimeEvent,
                        'queuename' => $entries[0]->queuename,
                        'logout_time' => $logoutTimeEvent,
                        'time_diff' => $formatted
                    ];
                }
            }

            return response()->json($temp);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage(), 500);
        }
    }
}
