<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use App\Models\SystemSetting;
use PAGI\Exception\ChannelDownException;
use PAMI\AsyncAgi\AsyncClientImpl;
use PAMI\Client\Impl\ClientImpl;
use PAMI\Message\Event\AsyncAGIStartEvent;
use PAMI\Message\Event\EventMessage;
use PAMI\Message\Action\AGIAction;
use PAMI\Message\Action\HangupAction;
use Illuminate\Support\Facades\DB;
use App\Models\IvrMenuSetting;
use PAMI\Message\Action\GotoAction;
use PAMI\Message\Action\OriginateAction;
use PAMI\Message\Action\RedirectAction;

class IvrMenu extends Command
{
    protected $signature = 'ivrmenu:start';
    protected $description = 'User customizable ivr menu';

    public function __construct()
    {
        parent::__construct();
    }

    protected function getAMIOptions(): array
    {
        return [
            'host' => SystemSetting::GetSetting('server_address'),
            'scheme' => 'tcp://',
            'port' => SystemSetting::GetSetting('manager_port'),
            'username' => SystemSetting::GetSetting('username'),
            'secret' => SystemSetting::GetSetting('secret'),
            'connect_timeout' => SystemSetting::GetSetting('connection_timeout'),
            'read_timeout' => SystemSetting::GetSetting('read_timeout')
        ];
    }

    public function handle()
    {
        $this->info("IVR MENU RUNNING");
        $client = new ClientImpl($this->getAMIOptions());

        $client->registerEventListener(function (EventMessage $event) use ($client) {
            if (!$event instanceof AsyncAGIStartEvent) {
                return;
            }

            $env = $event->getKey('env');
            $this->info("ARG : $env");
            preg_match('/agi_arg_1:\s*(\S+)/', $env, $matches);
            $scriptArg = $matches[1] ?? null;
            $this->info("ARG_VALUE : $scriptArg");
            if ($scriptArg !== 'IvrMenu') {
                return;
            }

            $channel = $event->getChannel();
            $extension = $event->getExten();

            // Log call session start
            $this->info("Starting IVR session for channel: $channel, extension: $extension");

            $agiClient = new AsyncClientImpl([
                'pamiClient' => $client,
                'asyncAgiEvent' => $event
            ]);

            // Process each call in isolation
            try {
                $this->processIVR($agiClient, $channel, $extension);
            } catch (\Exception $e) {
                $this->error("Error in IVR session for channel $channel: {$e->getMessage()}");
            }
        }, function ($event) {
            return $event instanceof AsyncAGIStartEvent;
        });

        $client->open();

        while (true) {
            $client->process();
        }
    }

    protected function processIVR($agiClient, $channel, $extension)
    {
        try {
            $this->info("IVR MENU Processing for channel: $channel");
            $agiClient->answer();

            $timingResult = $this->checkTimings();
            $this->info("Timing Res for channel $channel: $timingResult");

            if ($timingResult == 0) {
                $this->info("Off-Timing for channel: $channel");
                $menuExit = $this->fetchMenu(null, 1); // Fixed variable usage

                if (!$menuExit) {
                    $this->info("No off-working menu found for channel $channel, running normal flow.");
                    $agiClient->asyncBreak(); // Exit AGI script and return to dialplan
                    return;
                }

                $agiClient->streamFile($menuExit->media);
                $this->info("Running off-hours menu for channel $channel: {$menuExit->media}");
                $agiClient->exec('hangup');
                return false;
            } else if ($timingResult == 3) {
                $this->info("No ivr settings found for channel $channel, running normal flow.");
                $agiClient->asyncBreak(); // Exit AGI script and return to dialplan
                return;
            }

            // Process priority 1 menu first
            $priorityMenu = $this->fetchMenuByPriority(1);

            if (!$priorityMenu) {
                $this->info("No priority menu found for channel $channel, running normal flow.");
                $agiClient->asyncBreak(); // Exit AGI script and return to dialplan
                return;
            }

            $priorityMenuOptions = $this->fetchMenuOptions($priorityMenu->id);
            $ivrSettings = IvrMenuSetting::query()->first();

            if ($priorityMenuOptions->isEmpty()) {
                $agiClient->streamFile($priorityMenu->media);
                $this->info("Priority menu has no options for channel $channel. Redirecting to $ivrSettings->queue context.");
                // Create separate AMI client for this specific call's redirect
                $redirectClient = new ClientImpl($this->getAMIOptions());
                $redirectAction = new RedirectAction($channel, $extension, $ivrSettings->queue, 1);
                $redirectClient->open();
                $redirectClient->send($redirectAction);
                $redirectClient->close();
                return;
            }

            $menuId = $priorityMenu->id ?? null; // Start with the root menu for this call

            do {
                $menu = $this->fetchMenu($menuId, null);
                if (!$menu) {
                    $this->info("Invalid menu ID $menuId for channel $channel");
                    $agiClient->streamFile('invalid');
                    break;
                }

                $this->info("Processing menu ID $menuId for channel $channel");

                // Check if the menu has options
                $menuOptions = $this->fetchMenuOptions($menuId);

                $hasOptionNumber = false;
                $hasQueue = false;
                $queueName = "";
                $digit = ""; // Initialize digit variable for this call session

                if ($menuOptions->isEmpty()) {
                    $this->info("Menu $menuId has no options for channel $channel");
                    $agiClient->streamFile($menu->media);
                    break;
                } else {
                    // Analyze menu options for this specific call
                    foreach ($menuOptions as $menuOption) {
                        if (!is_null($menuOption->option_number)) {
                            $hasOptionNumber = true;
                        }
                        if (!is_null($menuOption->queue)) {
                            $hasQueue = true;
                            $queueName = $menuOption->queue;
                        }
                    }

                    if ($hasOptionNumber) {
                        // Get DTMF input for this specific call
                        $digit = $agiClient->getData($menu->media, 5000, 1)->getDigits();
                        $this->info("Channel $channel pressed digit: '$digit'");
                    } elseif ($hasQueue) {
                        $agiClient->streamFile($menu->media);
                        $this->info("Channel $channel: Menu has no digit options. Redirecting to $queueName context.");
                        // Create separate AMI client for this specific call's redirect
                        $redirectClient = new ClientImpl($this->getAMIOptions());
                        $redirectAction = new RedirectAction($channel, $extension, $queueName, 1);
                        $redirectClient->open();
                        $redirectClient->send($redirectAction);
                        $redirectClient->close();
                        return;
                    } else {
                        $this->info("Channel $channel: No option number or queue available.");
                    }
                }

                // Handle empty digit input for this specific call
                if (empty($digit) || $digit == "") {
                    if ($hasQueue) {
                        $this->info("Channel $channel: No digit pressed, redirecting to queue $queueName");
                        // Create separate AMI client for this specific call's redirect
                        $redirectClient = new ClientImpl($this->getAMIOptions());
                        $redirectAction = new RedirectAction($channel, $extension, $queueName, 1);
                        $redirectClient->open();
                        $redirectClient->send($redirectAction);
                        $redirectClient->close();
                        return;
                    }

                    $this->info("Channel $channel: User did not press any digit. Asking user to press a key to continue.");
                    $agiClient->streamFile('ivr/press-digit');
                    continue;
                }

                // Process user input for this specific call
                $nextMenuId = $this->processUserInput($menuId, $digit, $channel, $extension);

                if ($nextMenuId === null) {
                    $this->info("Channel $channel: Invalid digit '$digit' pressed for menu $menuId");
                    $agiClient->streamFile('invalid');
                } else {
                    $this->info("Channel $channel: Moving to next menu ID: $nextMenuId");
                    $menuId = $nextMenuId;
                }
            } while ($menuId);

            $this->info("Channel $channel: IVR session completed, hanging up");
            $agiClient->exec('hangup');
        } catch (ChannelDownException $e) {
            $this->error("Channel $channel down: {$e->getMessage()}");
        } catch (\Exception $e) {
            $this->error("Error processing IVR for channel $channel: {$e->getMessage()}");
            $this->error("Stack trace: " . $e->getTraceAsString());
        }
    }

    protected function fetchMenuByPriority($priority)
    {
        return DB::table('menus')->where('priority', $priority)->first();
    }

    protected function fetchMenu($menuId = null, $options = null)
    {
        $result = DB::table('menus');

        if ($menuId != null) {
            $result = $result->where('id', $menuId);
        } else if ($options != null) {
            $result = $result->where('off_working', $options);
        }

        return $result->first();
    }

    protected function processUserInput($menuId, $input, $channel, $extension)
    {
        $this->info("Channel $channel: Processing input '$input' for menu $menuId");

        // Fetch the option based on menu ID and input
        $option = DB::table('menu_options')->where('menu_id', $menuId)->where('option_number', $input)->first();

        if ($option) {
            // If target_menu_id is null, check the queue and redirect to the context
            if (is_null($option->target_menu_id)) {
                $queueContext = $option->queue;

                if (!empty($queueContext)) {
                    $this->info("Channel $channel: Redirecting to queue context: $queueContext");

                    // Create separate AMI client for this specific call's redirect
                    $redirectClient = new ClientImpl($this->getAMIOptions());
                    $action = new RedirectAction($channel, $extension, $queueContext, 1);
                    $redirectClient->open();
                    $response = $redirectClient->send($action);
                    $redirectClient->close();
                    return $response->getMessage();
                }
            }

            // Return the target_menu_id if it exists
            $this->info("Channel $channel: Moving to target menu: {$option->target_menu_id}");
            return $option->target_menu_id;
        }

        // Return null if no matching option is found
        $this->info("Channel $channel: No matching option found for input '$input' in menu $menuId");
        return null;
    }


    protected function fetchMenuOptions($menuId)
    {
        return DB::table('menu_options')->where('menu_id', $menuId)->get();
    }

    private function checkTimings()
    {
        $settings = IvrMenuSetting::query()->first();

        if (!$settings) {
            return 3;
        }

        $now = Carbon::now();
        $start = Carbon::parse($settings->start);
        $end = Carbon::parse($settings->end);

        if ($now->between($start, $end)) {
            return 1;
        }

        return 0;
    }
}
